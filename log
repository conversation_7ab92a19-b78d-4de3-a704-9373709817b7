08:33:36.762 [http-nio-5080-exec-3] ERROR c.f.c.u.h.HttpUtils - [sendGet,97] - 调用HttpUtils.sendGet IOException, url=http://whois.pconline.com.cn/ipJson.jsp,param=ip=************&json=true
java.io.IOException: Server returned HTTP response code: 503 for URL: http://whois.pconline.com.cn/ipJson.jsp?ip=************&json=true
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1951)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1549)
	at com.fastbee.common.utils.http.HttpUtils.sendGet(HttpUtils.java:79)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.setLocation(DeviceServiceImpl.java:1416)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.updateDeviceStatusAndLocation(DeviceServiceImpl.java:1082)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.updateDeviceStatusAndLocation(<generated>)
	at com.fastbee.data.controller.ToolController.webHookProcessv5(ToolController.java:390)
	at com.fastbee.data.controller.ToolController$$FastClassBySpringCGLIB$$63407c17.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.data.controller.ToolController$$EnhancerBySpringCGLIB$$ad00a7c9.webHookProcessv5(<generated>)
	at sun.reflect.GeneratedMethodAccessor242.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
08:34:06.817 [http-nio-5080-exec-39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1074 millis. update iot_device
         SET status = ?,
            alarm_status = ?,
            network_address = ?,
            network_ip = ?,
            
            
            active_time = ? 
        where serial_number = ? or gw_dev_code = ? [3,0,"山西省 移动","*************","2025-04-30 08:00:00","HaiNaP1","HaiNaP1"]
08:34:09.668 [deviceUpMessageTask4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1431 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:34:08.915 [sip-14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1034 millis. select device_id,
               product_id,
               product_name,
               device_sip_id,
               device_name,
               manufacturer,
               model,
               firmware,
               transport,
               streamMode,
               online,
               registerTime,
               lastConnectTime,
               active_time,
               ip,
               port,
               hostAddress,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sip_device
     
        where device_sip_id = ? ["44060400002000000001"]
08:34:09.668 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2139 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,

content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,


create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["线上巡检测试","94","线上巡检测试",0,"2025-08-09","month",0,"0","160","7","2","0",1,"huxl","2025-07-09 15:44:24",null,"2025-07-31 08:34:07","1942852317137195010"]
08:34:10.017 [http-nio-5080-exec-39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1426 millis. insert into iot_event_log
         ( identity,
            
            log_type,
            log_value,
            device_id,
            device_name,
            serial_number,
            is_monitor,
            mode,
            user_id,
            user_name,
            
            
            
            create_time,
            remark ) 
         values ( ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            ?,
            ? ) ["online",5,"1",312,"4G无线压力传感器94","HaiNaP1",0,3,1,"admin","2025-07-31 08:34:07","设备上线"]
08:34:11.587 [deviceUpMessageTask20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1570 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"L_Pressure_1_HighLimit"]
08:34:11.587 [deviceUpMessageTask4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1216 millis. select p.protocol_code from
        iot_product p
        where p.product_id = ? [146]
08:34:11.588 [deviceUpMessageTask14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1570 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:34:11.937 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1060 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [147,1,1]
08:34:13.107 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2229 millis. SELECT * FROM iot_keep_data d
      left join iot_keep_plan p on d.plan_id = p.id
      WHERE 1=1
       
          and d.plan_id = ?
       
      order by d.create_time desc
      limit 1 ["1940695257754636290"]
08:34:14.756 [deviceUpMessageTask20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1503 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_2_Level"]
08:34:18.821 [sip-14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1450 millis. update sip_device
         SET online = ?,
            lastConnectTime = ?,
            
            ip = ?,
            port = ?,
            hostaddress = ? 
        where device_sip_id = ? ["","2025-07-31 08:34:16","************",7100,"************:7100","44060400002000000001"]
08:34:24.156 [deviceUpMessageTask3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1131 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["XJYJ"]
08:34:24.500 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1098 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [146,1,1]
08:34:24.501 [sip-14] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.GeneratedMethodAccessor789.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
08:34:25.237 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1835 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,

content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,
patrol_type=?,
position_range=?,
create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["线下巡检冲刺冲刺","94,96,99","线下巡检冲刺冲刺",0,"2025-08-01","day",0,"0","76","3","3","0",1,"location","500","huxl","2025-07-03 15:22:01",null,"2025-07-31 08:34:22","1941034746729578497"]
08:34:25.238 [http-nio-5080-exec-63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1835 millis. insert into iot_event_log
         ( identity,
            
            log_type,
            log_value,
            device_id,
            device_name,
            serial_number,
            is_monitor,
            mode,
            user_id,
            user_name,
            
            
            
            create_time,
            remark ) 
         values ( ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            ?,
            ? ) ["offline",6,"0",62245,"4G压力传感器厂家测试","Pressure001",0,3,1,"admin","2025-07-31 08:34:22","设备离线"]
08:34:27.092 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1056 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pump_2_Freq"]
08:34:28.151 [deviceUpMessageTask17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1403 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"7.79","XJYJ","O_PH_5_Water",0,2,"2025-07-31 08:34:14","其他区PH计5水"]
08:34:29.567 [deviceUpMessageTask14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1075 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["BIGUIGUANGCHANG",146]
08:34:31.405 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1145 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [147,1,1]
08:34:31.790 [deviceUpMessageTask11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1145 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_10_Water"]
08:34:31.790 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1145 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Out"]
08:34:32.505 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1100 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_HighLimit"]
08:34:32.505 [deviceUpMessageTask3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1099 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_3_Water"]
08:34:33.924 [deviceUpMessageTask9] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1766 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["XJYJ"]
08:34:33.925 [deviceUpMessageTask4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1408 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["BIGUIGUANGCHANG",146]
08:34:31.790 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1145 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,

content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,
patrol_type=?,

create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["西南与云东海线下巡检计划","103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,123,124,125,126,127,1...","西南与云东海线下巡检计划",0,"2025-07-14","1",0,"0","183","8","3","0",1,"location","liuqr","2025-07-03 11:33:06",null,"2025-07-31 08:34:28","1940614748605345794"]
08:34:39.648 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1485 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pump_1_Freq"]
08:34:42.703 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1831 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_HighLimit"]
08:34:42.704 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1842 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_HighLimit"]
08:34:42.337 [deviceUpMessageTask3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1831 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_7_Water"]
08:34:47.166 [deviceUpMessageTask9] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2397 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["XJYJ"]
08:34:44.431 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2139 millis. SELECT * FROM iot_keep_data d
      left join iot_keep_plan p on d.plan_id = p.id
      WHERE 1=1
       
          and d.plan_id = ?
       
      order by d.create_time desc
      limit 1 ["1940582535939584001"]
08:34:50.883 [http-nio-5080-exec-29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 3370 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["Pressure001",141]
08:34:50.883 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 3023 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:34:52.250 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 5731 millis. select product_id, product_name,protocol_code,transport, category_id, category_name, tenant_id, tenant_name, is_sys, is_authorize, enable_dynamic_model, mqtt_account,mqtt_password,mqtt_secret ,status, device_type, network_method, vertificate_method, create_time, update_time, img_url,remark,location_way,tank_count,pump_count,supply_area from iot_product
     
        where product_id = ? [146]
08:34:54.031 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1049 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:34:57.207 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1017 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [146,1,1]
08:34:57.554 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2431 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:34:56.181 [deviceUpMessageTask9] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1404 millis. select img_url
        from iot_product
        where product_id = ? [147]
08:34:57.207 [deviceUpMessageTask3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1357 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_9_Water"]
08:34:56.179 [http-nio-5080-exec-63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1402 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["Pressure001",141]
08:34:59.632 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1728 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:35:03.329 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1091 millis. select p.protocol_code from
        iot_product p
        where p.product_id = ? [146]
08:35:04.759 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1724 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,

content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,


create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["海纳珑庭","134","二供改造小区巡检",0,"2025-08-01","day",0,"0","1877547766784221186","6","2","0",1,"13516500443","2025-07-03 09:25:06","cenqh","2025-07-31 08:34:55","1940582535939584001"]
08:35:02.991 [dynamic-model-pool-21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 3709 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_HighLimit"]
08:35:04.356 [http-nio-5080-exec-8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2500 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["Pressure001",141]
08:35:13.440 [sip-15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 5775 millis. update sip_device
         SET online = ?,
            lastConnectTime = ?,
            
            ip = ?,
            port = ?,
            hostaddress = ? 
        where device_sip_id = ? ["","2025-07-31 08:34:54","************",7100,"************:7100","44060400002000000001"]
08:35:12.742 [dynamic-model-pool-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 8015 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_HighLimit"]
08:35:22.728 [dynamic-model-pool-21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1139 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Out"]
08:35:22.727 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1139 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Set"]
08:35:22.728 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1139 millis. SELECT id,name,village_ids,task_ids,content,times,gen_time_str,cycle,cycle_len,start_date,end_date,reminder,priority,status,remark,director_id,build_type,organization_id,plan_type,is_del,is_gen_now,patrol_type,position_range,create_by,create_time,update_by,update_time FROM iot_keep_plan WHERE id=?  ["1940582362899378178"]
08:35:22.727 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1136 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"L_Pump_1_Freq"]
08:35:25.117 [dynamic-model-pool-13] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [releaseLock,970] - 释放分布式锁出错
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.del(LettuceKeyCommands.java:90)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.del(DefaultedRedisConnection.java:83)
	at org.springframework.data.redis.core.RedisTemplate.lambda$delete$2(RedisTemplate.java:708)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.RedisTemplate.delete(RedisTemplate.java:708)
	at com.fastbee.common.core.redis.RedisCache.deleteObject(RedisCache.java:119)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.releaseLock(DynamicThingsModelServiceImpl.java:968)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageInternal(DynamicThingsModelServiceImpl.java:545)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageTask(DynamicThingsModelServiceImpl.java:421)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:398)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 17 common frames omitted
08:35:25.117 [deviceUpMessageTask7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1157 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:35:25.573 [dynamic-model-pool-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1613 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pump_1_Freq"]
08:35:25.118 [http-nio-5080-exec-48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1158 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["HaiNaP1",141]
08:35:27.421 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1122 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pump_1_Freq"]
08:35:27.421 [deviceUpMessageTask7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1121 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:35:27.421 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1121 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"L_Pressure_1_HighLimit"]
08:35:27.421 [deviceUpMessageTask16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1121 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["BIGUIGUANGCHANG"]
08:35:27.421 [http-nio-5080-exec-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1121 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["HaiNaP2",141]
08:35:28.811 [sip-15] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.GeneratedMethodAccessor789.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
08:35:32.117 [quartzScheduler_Worker-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1140 millis. insert into sys_job_log(
 			 
 			 job_name, 
 			 job_group, 
 			 invoke_target, 
 			 job_message, 
 			 status, 
 			 
 			create_time
 		)values(
 			 
 			 ?, 
 			 ?, 
 			 ?, 
 			 ?, 
 			 ?, 
 			 
 			sysdate()
 		) ["巡检工单生成定时任务","DEFAULT","workOrderTask.generateWorkOrder()","巡检工单生成定时任务 总共耗时：88624毫秒","0"]
08:35:33.591 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1101 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"5529","BIGUIGUANGCHANG","H_Pump_2_Freq",0,2,"2025-07-31 08:35:22","2号泵频率"]
08:35:34.386 [deviceUpMessageTask4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1165 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pump_1_Freq"]
08:35:34.387 [deviceUpMessageTask5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1165 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["BIGUIGUANGCHANG"]
08:35:34.386 [deviceUpMessageTask8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1165 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_5_Water"]
08:35:34.387 [deviceUpMessageTask16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1165 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Set"]
08:35:34.387 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1165 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:35:34.812 [deviceUpMessageTask9] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1219 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_8_Water"]
08:35:36.922 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1384 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"1.6","BIGUIGUANGCHANG","H_Pressure_1_HighLimit",0,2,"2025-07-31 08:35:30","压力上限（MPa）"]
08:35:37.690 [deviceUpMessageTask7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1122 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Set"]
08:35:38.051 [deviceUpMessageTask5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1129 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:35:38.051 [deviceUpMessageTask8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1128 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_7_Water"]
08:35:38.048 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1124 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [147,1,1]
08:35:38.810 [deviceUpMessageTask16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1116 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:35:38.812 [deviceUpMessageTask4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1117 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Set"]
08:35:39.893 [deviceUpMessageTask5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1085 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:35:41.317 [sip-16] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.GeneratedMethodAccessor789.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
08:35:42.085 [deviceUpMessageTask18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1475 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pump_1_Freq"]
08:35:42.085 [deviceUpMessageTask7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1825 millis. select product_id, product_name,protocol_code,transport, category_id, category_name, tenant_id, tenant_name, is_sys, is_authorize, enable_dynamic_model, mqtt_account,mqtt_password,mqtt_secret ,status, device_type, network_method, vertificate_method, create_time, update_time, img_url,remark,location_way,tank_count,pump_count,supply_area from iot_product
     
        where product_id = ? [146]
08:35:42.086 [deviceUpMessageTask8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1473 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_9_Water"]
08:35:42.438 [deviceUpMessageTask4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1825 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_HighLimit"]
08:35:44.304 [deviceUpMessageTask13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2986 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_3_Water"]
08:35:45.686 [deviceUpMessageTask14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1382 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["XJYJ"]
08:35:46.022 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1514 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,

content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,


create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["测试计划","100","测试计划",0,"2025-08-01","day",0,"0","16","2","2","0",1,"ccCs","2025-07-10 10:21:23","ccCs","2025-07-31 08:35:40","1943133415188582401"]
08:35:47.360 [deviceUpMessageTask8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1673 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_10_Water"]
08:35:50.303 [deviceUpMessageTask13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1075 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_5_Water"]
08:35:51.058 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1472 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"41","BIGUIGUANGCHANG","L_Pump_1_Freq",0,2,"2025-07-31 08:35:36","低压区泵1频率"]
08:35:51.753 [deviceUpMessageTask5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1081 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pump_1_Freq"]
08:35:54.540 [deviceUpMessageTask13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1065 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_6_Water"]
08:35:55.612 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2137 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [146,1,1]
08:35:55.612 [deviceUpMessageTask17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1072 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:35:55.613 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1072 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["BIGUIGUANGCHANG",146]
08:35:55.239 [sip-19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1764 millis. update sip_device
         SET device_sip_id = ?,
            
            
            
            
            transport = ?,
            streamMode = ?,
            
            registerTime = ?,
            
            
            ip = ?,
            port = ?,
            hostAddress = ? 
        where device_id = ? ["44060400002000000001","UDP","UDP","2025-07-31 08:35:48","************",7100,"************:7100",21]
08:35:57.365 [deviceUpMessageTask14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2125 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_3_Water"]
08:35:58.368 [deviceUpMessageTask18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1368 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_HighLimit"]
08:35:59.457 [deviceUpMessageTask5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1763 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:35:59.457 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2456 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,

content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,


create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["线上巡检测试","94","线上巡检测试",0,"2025-08-09","month",0,"0","160","7","2","0",1,"huxl","2025-07-09 15:44:24",null,"2025-07-31 08:35:53","1942852317137195010"]
08:35:59.094 [deviceUpMessageTask20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1353 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Out"]
08:36:00.473 [deviceUpMessageTask13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1755 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_7_Water"]
08:36:02.176 [sip-20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 5175 millis. update sip_device
         SET device_sip_id = ?,
            
            
            
            
            transport = ?,
            streamMode = ?,
            
            registerTime = ?,
            
            
            ip = ?,
            port = ?,
            hostAddress = ? 
        where device_id = ? ["44060400002000000001","UDP","UDP","2025-07-31 08:35:50","************",7100,"************:7100",21]
08:36:02.498 [deviceUpMessageTask15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2693 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["BIGUIGUANGCHANG"]
08:36:04.669 [deviceUpMessageTask20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2493 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Set"]
08:36:15.365 [deviceUpMessageTask14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 11397 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_4_Water"]
08:36:12.776 [deviceUpMessageTask5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 5577 millis. select product_id, product_name,protocol_code,transport, category_id, category_name, tenant_id, tenant_name, is_sys, is_authorize, enable_dynamic_model, mqtt_account,mqtt_password,mqtt_secret ,status, device_type, network_method, vertificate_method, create_time, update_time, img_url,remark,location_way,tank_count,pump_count,supply_area from iot_product
     
        where product_id = ? [146]
08:36:08.076 [deviceUpMessageTask17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 3830 millis. select p.protocol_code from
        iot_product p
        where p.product_id = ? [146]
08:36:03.205 [sip-19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2059 millis. select id,
               tenant_id,
               tenant_name,
               product_id,
               product_name,
               user_id,
               user_name,
               device_sip_id,
               channel_sip_id,
               channel_name,
               register_time,
               device_type,
               channel_type,
               cityCode,
               civilCode,
               manufacture,
               model,
               owner,
               block,
               address,
               parentId,
               ipAddress,
               port,
               password,
               PTZType,
               PTZTypeText,
               status,
               longitude,
               latitude,
               streamId,
               subCount,
               parental,
               hasAudio,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sip_device_channel
     
        where device_sip_id = ? ["44060400002000000001"]
08:36:18.852 [deviceUpMessageTask3] ERROR c.f.i.s.i.DeviceDataChangeServiceImpl - [shouldSaveData,85] - 数据变化检测异常，默认存储 - 设备: XJYJ, 标识: O_PH_9_Water, 值: 45.18
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DeviceDataChangeServiceImpl.shouldSaveData(DeviceDataChangeServiceImpl.java:48)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:372)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 41 common frames omitted
08:36:18.852 [deviceUpMessageTask7] ERROR c.f.i.s.i.DeviceDataChangeServiceImpl - [shouldSaveData,85] - 数据变化检测异常，默认存储 - 设备: BIGUIGUANGCHANG, 标识: H_Pressure_1_Out, 值: 1.39
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DeviceDataChangeServiceImpl.shouldSaveData(DeviceDataChangeServiceImpl.java:48)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:372)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 41 common frames omitted
08:36:19.227 [deviceUpMessageTask9] ERROR c.f.i.s.i.DeviceDataChangeServiceImpl - [shouldSaveData,85] - 数据变化检测异常，默认存储 - 设备: XJYJ, 标识: O_PH_3_Water, 值: 2.82
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DeviceDataChangeServiceImpl.shouldSaveData(DeviceDataChangeServiceImpl.java:48)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:372)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:176)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:995)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
08:36:19.940 [deviceUpMessageTask10] ERROR c.f.m.s.i.DataHandlerImpl - [reportData,100] - 接收属性数据，解析数据时异常 message=Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s),e={}
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.incrBy(LettuceStringCommands.java:202)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.incrBy(DefaultedRedisConnection.java:351)
	at org.springframework.data.redis.core.DefaultValueOperations.lambda$increment$1(DefaultValueOperations.java:98)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.increment(DefaultValueOperations.java:98)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.incrementAndGet(RedisAtomicLong.java:283)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.getAndIncrement(RedisAtomicLong.java:209)
	at com.fastbee.common.core.redis.RedisCache.incr2(RedisCache.java:291)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:215)
	at com.fastbee.mqtt.manager.MqttRemoteManager.pushCommon(MqttRemoteManager.java:87)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:91)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:176)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:995)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
08:36:19.940 [dynamic-model-pool-21] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [tryLock,956] - 获取分布式锁出错
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.getOrElse(LettuceInvoker.java:598)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$SingleInvocationSpec.orElse(LettuceInvoker.java:385)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:113)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:295)
	at org.springframework.data.redis.core.DefaultValueOperations.lambda$setIfAbsent$11(DefaultValueOperations.java:310)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.setIfAbsent(DefaultValueOperations.java:310)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.tryLock(DynamicThingsModelServiceImpl.java:953)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageInternal(DynamicThingsModelServiceImpl.java:504)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageTask(DynamicThingsModelServiceImpl.java:421)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:398)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 18 common frames omitted
08:36:19.940 [sip-20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1092 millis. select id,
               tenant_id,
               tenant_name,
               product_id,
               product_name,
               user_id,
               user_name,
               device_sip_id,
               channel_sip_id,
               channel_name,
               register_time,
               device_type,
               channel_type,
               cityCode,
               civilCode,
               manufacture,
               model,
               owner,
               block,
               address,
               parentId,
               ipAddress,
               port,
               password,
               PTZType,
               PTZTypeText,
               status,
               longitude,
               latitude,
               streamId,
               subCount,
               parental,
               hasAudio,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sip_device_channel
     
        where device_sip_id = ? ["44060400002000000001"]
08:36:20.725 [dynamic-model-pool-12] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [tryLock,956] - 获取分布式锁出错
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.getOrElse(LettuceInvoker.java:598)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$SingleInvocationSpec.orElse(LettuceInvoker.java:385)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:113)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:295)
	at org.springframework.data.redis.core.DefaultValueOperations.lambda$setIfAbsent$11(DefaultValueOperations.java:310)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.setIfAbsent(DefaultValueOperations.java:310)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.tryLock(DynamicThingsModelServiceImpl.java:953)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageInternal(DynamicThingsModelServiceImpl.java:504)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageTask(DynamicThingsModelServiceImpl.java:421)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:398)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 18 common frames omitted
08:36:21.218 [dynamic-model-pool-9] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [processMessageInternal,549] - 处理标识符时出错: O_PH_9_Water
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageInternal(DynamicThingsModelServiceImpl.java:508)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageTask(DynamicThingsModelServiceImpl.java:421)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:398)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 18 common frames omitted
08:36:20.725 [deviceUpMessageTask16] ERROR c.f.i.s.i.DeviceDataChangeServiceImpl - [shouldSaveData,85] - 数据变化检测异常，默认存储 - 设备: BIGUIGUANGCHANG, 标识: M_Pump_1_Freq, 值: 44
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DeviceDataChangeServiceImpl.shouldSaveData(DeviceDataChangeServiceImpl.java:48)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:372)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 41 common frames omitted
08:36:22.721 [deviceUpMessageTask13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1060 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_9_Water"]
08:36:23.106 [deviceUpMessageTask15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1110 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:36:23.106 [http-nio-5080-exec-98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1110 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["HaiNaP2",141]
08:36:23.478 [deviceUpMessageTask20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1122 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_HighLimit"]
08:36:25.034 [http-nio-5080-exec-33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1177 millis. select device_id, device_name,product_id, serial_number,tenant_id, tenant_name, status, alarm_status, is_shadow,is_simulate, rssi ,location_way,things_model_value, active_time from iot_device
     
        where serial_number = ? ["Pressure001"]
08:36:27.947 [deviceUpMessageTask20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1086 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:36:28.342 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1418 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"1.3","BIGUIGUANGCHANG","M_Pressure_1_Out",0,2,"2025-07-31 08:35:41","中压区水压表计1出口"]
08:36:29.088 [deviceUpMessageTask17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1140 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"L_Pressure_1_HighLimit"]
08:36:30.172 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1084 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"1.5","BIGUIGUANGCHANG","M_Pressure_1_HighLimit",0,2,"2025-07-31 08:36:19","中压区水压表计1高报警设置"]
08:36:30.863 [deviceUpMessageTask17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1021 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_2_Level"]
08:36:30.864 [deviceUpMessageTask7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1023 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"5530","BIGUIGUANGCHANG","H_Pump_2_Freq",0,2,"2025-07-31 08:35:54","2号泵频率"]
08:36:31.606 [deviceUpMessageTask15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1092 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pump_2_Freq"]
08:36:31.606 [http-nio-5080-exec-79] ERROR c.f.d.c.ToolController - [webHookProcessv5,418] - 发生错误: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceHashCommands.hGetAll(LettuceHashCommands.java:124)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.hGetAll(DefaultedRedisConnection.java:1152)
	at org.springframework.data.redis.core.DefaultHashOperations.lambda$entries$13(DefaultHashOperations.java:245)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultHashOperations.entries(DefaultHashOperations.java:245)
	at org.springframework.data.redis.core.DefaultBoundHashOperations.entries(DefaultBoundHashOperations.java:183)
	at com.fastbee.common.core.redis.RedisCache.hashEntity(RedisCache.java:494)
	at com.fastbee.iot.service.cache.impl.ThingModelCacheImpl.getCacheDeviceStatus(ThingModelCacheImpl.java:47)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.selectShortDeviceBySerialNumber(DeviceServiceImpl.java:255)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.selectShortDeviceBySerialNumber(<generated>)
	at com.fastbee.data.controller.ToolController.webHookProcessv5(ToolController.java:360)
	at com.fastbee.data.controller.ToolController$$FastClassBySpringCGLIB$$63407c17.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.data.controller.ToolController$$EnhancerBySpringCGLIB$$ad00a7c9.webHookProcessv5(<generated>)
	at sun.reflect.GeneratedMethodAccessor242.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 139 common frames omitted
08:36:32.708 [http-nio-5080-exec-79] ERROR c.f.d.c.ToolController - [webHookProcessv5,422] - 错误位置: org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert:70
08:36:31.606 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1093 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["XJYJ"]
08:36:33.077 [deviceUpMessageTask17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1470 millis. select product_id, product_name,protocol_code,transport, category_id, category_name, tenant_id, tenant_name, is_sys, is_authorize, enable_dynamic_model, mqtt_account,mqtt_password,mqtt_secret ,status, device_type, network_method, vertificate_method, create_time, update_time, img_url,remark,location_way,tank_count,pump_count,supply_area from iot_product
     
        where product_id = ? [146]
08:36:33.078 [deviceUpMessageTask7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1106 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"1.39","BIGUIGUANGCHANG","H_Pressure_1_Out",0,2,"2025-07-31 08:36:19","出水压力"]
08:36:33.446 [http-nio-5080-exec-33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2582 millis. update iot_device
         SET status = ?,
            alarm_status = ?,
            
            
            
            
            active_time = ? 
        where serial_number = ? or gw_dev_code = ? [4,0,"2025-07-16 08:00:00","Pressure001","Pressure001"]
08:36:34.152 [sip-21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1841 millis. update sip_device
         SET online = ?,
            lastConnectTime = ?,
            
            ip = ?,
            port = ?,
            hostaddress = ? 
        where device_sip_id = ? ["","2025-07-31 08:36:29","************",7100,"************:7100","44060400002000000001"]
08:36:34.152 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1073 millis. SELECT id,name,village_ids,task_ids,content,times,gen_time_str,cycle,cycle_len,start_date,end_date,reminder,priority,status,remark,director_id,build_type,organization_id,plan_type,is_del,is_gen_now,patrol_type,position_range,create_by,create_time,update_by,update_time FROM iot_keep_plan WHERE id=?  ["1940695257754636290"]
08:36:34.152 [deviceUpMessageTask14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1073 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_10_Water"]
08:36:34.518 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1072 millis. select img_url
        from iot_product
        where product_id = ? [147]
08:36:34.519 [deviceUpMessageTask15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1073 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Out"]
08:36:35.247 [http-nio-5080-exec-33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1095 millis. insert into iot_event_log
         ( identity,
            
            log_type,
            log_value,
            device_id,
            device_name,
            serial_number,
            is_monitor,
            mode,
            user_id,
            user_name,
            
            
            
            create_time,
            remark ) 
         values ( ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            ?,
            ? ) ["offline",6,"0",62245,"4G压力传感器厂家测试","Pressure001",0,3,1,"admin","2025-07-31 08:36:33","设备离线"]
08:36:35.986 [deviceUpMessageTask6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1110 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["BIGUIGUANGCHANG",146]
08:36:36.368 [deviceUpMessageTask15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1119 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Set"]
08:36:38.879 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1450 millis. SELECT * FROM iot_keep_data d
      left join iot_keep_plan p on d.plan_id = p.id
      WHERE 1=1
       
          and d.plan_id = ?
       
      order by d.create_time desc
      limit 1 ["1940692374875279361"]
08:36:40.442 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2293 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_3_Water"]
08:36:39.673 [sip-21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1158 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["44060400002000000001"]
08:36:40.442 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2293 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["BIGUIGUANGCHANG"]
08:36:43.811 [deviceUpMessageTask15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1444 millis. select product_id, product_name,protocol_code,transport, category_id, category_name, tenant_id, tenant_name, is_sys, is_authorize, enable_dynamic_model, mqtt_account,mqtt_password,mqtt_secret ,status, device_type, network_method, vertificate_method, create_time, update_time, img_url,remark,location_way,tank_count,pump_count,supply_area from iot_product
     
        where product_id = ? [146]
08:36:44.177 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2211 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:36:44.536 [deviceUpMessageTask16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 6021 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"44","BIGUIGUANGCHANG","M_Pump_1_Freq",0,2,"2025-07-31 08:36:21","中压区泵1频率"]
08:36:47.049 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1427 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["BIGUIGUANGCHANG"]
08:36:47.423 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1430 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:36:47.860 [sip-22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2593 millis. select device_id,
               product_id,
               product_name,
               device_sip_id,
               device_name,
               manufacturer,
               model,
               firmware,
               transport,
               streamMode,
               online,
               registerTime,
               lastConnectTime,
               active_time,
               ip,
               port,
               hostAddress,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sip_device
     
        where device_sip_id = ? ["44060400002000000001"]
08:36:49.312 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1094 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:36:48.574 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1150 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_5_Water"]
08:36:52.180 [MQTT Call: server-*************] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1080 millis. select script_id from iot_script
         WHERE  product_id = ?
            
             and script_event = ?
            
             and script_purpose = ? 
        order by script_order [146,1,1]
08:36:51.814 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2124 millis. select p.protocol_code from
        iot_product p
        where p.product_id = ? [146]
08:36:52.540 [sip-22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1081 millis. update sip_device
         SET online = ?,
            lastConnectTime = ?,
            
            ip = ?,
            port = ?,
            hostaddress = ? 
        where device_sip_id = ? ["","2025-07-31 08:36:49","************",7100,"************:7100","44060400002000000001"]
08:36:53.236 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1776 millis. UPDATE iot_keep_plan  SET name=?,
village_ids=?,
task_ids=?,
content=?,
times=?,
gen_time_str=?,
cycle=?,
cycle_len=?,




status=?,

director_id=?,

organization_id=?,
plan_type=?,
is_del=?,
is_gen_now=?,


create_by=?,
create_time=?,
update_by=?,
update_time=?  WHERE id=? ["周一周四执行水箱清洁","94","31","周一周四执行水箱清洁",0,"2025-07-03","1,4",0,"0","76","3","1","0",0,"huxl","2025-07-03 16:41:33",null,"2025-07-31 08:36:46","1940692374875279361"]
08:36:53.235 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1055 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:36:54.320 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1084 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Out"]
08:36:53.236 [sip-21] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.GeneratedMethodAccessor789.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
08:36:55.735 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1058 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pump_2_Freq"]
08:36:55.398 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1436 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_7_Water"]
08:36:57.585 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1035 millis. SELECT id,name,village_ids,task_ids,content,times,gen_time_str,cycle,cycle_len,start_date,end_date,reminder,priority,status,remark,director_id,build_type,organization_id,plan_type,is_del,is_gen_now,patrol_type,position_range,create_by,create_time,update_by,update_time FROM iot_keep_plan WHERE id=?  ["1941034746729578497"]
08:36:58.304 [deviceUpMessageTask11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1754 millis. select device_id, device_name, product_id, product_name, tenant_id, tenant_name, serial_number,gw_dev_code, firmware_version, status, alarm_status, rssi,is_shadow ,is_simulate,location_way,things_model_value,network_address, network_ip, longitude, latitude, active_time, create_time, update_time, img_url,summary,remark,slave_id from iot_device
     
         WHERE  gw_dev_code = ? 
        order by create_time desc ["BIGUIGUANGCHANG"]
08:36:59.048 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1463 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Out"]
08:36:58.304 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1404 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Out"]
08:37:00.110 [deviceUpMessageTask18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2170 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["BIGUIGUANGCHANG",146]
08:37:00.843 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1428 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_8_Water"]
08:37:00.843 [deviceUpMessageTask10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1428 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["XJYJ"]
08:37:04.454 [http-nio-5080-exec-53] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2911 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["HaiNaP2",141]
08:37:02.631 [deviceUpMessageTask1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2520 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pump_1_Freq"]
08:37:02.979 [deviceUpMessageTask11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2868 millis. select img_url
        from iot_product
        where product_id = ? [146]
08:37:37.165 [deviceUpMessageTask18] ERROR c.f.m.s.i.DataHandlerImpl - [reportData,100] - 接收属性数据，解析数据时异常 message=Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s),e={}
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.setNX(LettuceStringCommands.java:126)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.setNX(DefaultedRedisConnection.java:302)
	at org.springframework.data.redis.core.DefaultValueOperations.lambda$setIfAbsent$10(DefaultValueOperations.java:296)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.setIfAbsent(DefaultValueOperations.java:296)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.initializeIfAbsent(RedisAtomicLong.java:151)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.<init>(RedisAtomicLong.java:96)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.<init>(RedisAtomicLong.java:65)
	at com.fastbee.common.core.redis.RedisCache.incr2(RedisCache.java:290)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:215)
	at com.fastbee.mqtt.manager.MqttRemoteManager.pushCommon(MqttRemoteManager.java:87)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:91)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:176)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:995)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
08:37:35.581 [dynamic-model-pool-18] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [releaseLock,970] - 释放分布式锁出错
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.del(LettuceKeyCommands.java:90)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.del(DefaultedRedisConnection.java:83)
	at org.springframework.data.redis.core.RedisTemplate.lambda$delete$2(RedisTemplate.java:708)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.RedisTemplate.delete(RedisTemplate.java:708)
	at com.fastbee.common.core.redis.RedisCache.deleteObject(RedisCache.java:119)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.releaseLock(DynamicThingsModelServiceImpl.java:968)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageInternal(DynamicThingsModelServiceImpl.java:545)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageTask(DynamicThingsModelServiceImpl.java:421)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:398)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 17 common frames omitted
08:37:43.123 [deviceUpMessageTask15] ERROR c.f.i.s.i.DeviceDataChangeServiceImpl - [shouldSaveData,85] - 数据变化检测异常，默认存储 - 设备: BIGUIGUANGCHANG, 标识: H_Pump_2_Freq, 值: 5529
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DeviceDataChangeServiceImpl.shouldSaveData(DeviceDataChangeServiceImpl.java:48)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:372)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 41 common frames omitted
08:37:06.627 [http-nio-5080-exec-87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 3648 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["HaiNaP2",141]
08:37:37.535 [deviceUpMessageTask12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 29102 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [147,"O_PH_9_Water"]
08:37:44.188 [sip-22] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.incrBy(LettuceStringCommands.java:202)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.incrBy(DefaultedRedisConnection.java:351)
	at org.springframework.data.redis.core.DefaultValueOperations.lambda$increment$1(DefaultValueOperations.java:98)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.increment(DefaultValueOperations.java:98)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.incrementAndGet(RedisAtomicLong.java:283)
	at org.springframework.data.redis.support.atomic.RedisAtomicLong.getAndIncrement(RedisAtomicLong.java:209)
	at com.fastbee.common.core.redis.RedisCache.incr2(RedisCache.java:291)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:215)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.GeneratedMethodAccessor789.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 30 common frames omitted
08:37:44.863 [deviceUpMessageTask17] ERROR c.f.i.s.i.DeviceDataChangeServiceImpl - [shouldSaveData,85] - 数据变化检测异常，默认存储 - 设备: BIGUIGUANGCHANG, 标识: L_Pump_1_Freq, 值: 41
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheObject(RedisCache.java:110)
	at com.fastbee.iot.service.impl.DeviceDataChangeServiceImpl.shouldSaveData(DeviceDataChangeServiceImpl.java:48)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:372)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 41 common frames omitted
08:37:38.974 [http-nio-5080-exec-11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 29841 millis. SELECT p.mqtt_password,p.mqtt_account, p.mqtt_secret,p.is_authorize,p.product_id,p.product_name,p.vertificate_method,p.STATUS as product_status,d.device_id,d.device_name,d.STATUS,d.serial_number
        FROM iot_product p
                LEFT JOIN ( SELECT device_id, device_name, STATUS, product_id, product_name, serial_number
                FROM iot_device
                WHERE serial_number = ? ) AS d ON d.product_id = p.product_id
        WHERE
            p.product_id = ? ["HaiNaP2",141]
08:37:42.788 [dynamic-model-pool-11] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [tryLock,956] - 获取分布式锁出错
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.getOrElse(LettuceInvoker.java:598)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$SingleInvocationSpec.orElse(LettuceInvoker.java:385)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:113)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:295)
	at org.springframework.data.redis.core.DefaultValueOperations.lambda$setIfAbsent$11(DefaultValueOperations.java:310)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.setIfAbsent(DefaultValueOperations.java:310)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.tryLock(DynamicThingsModelServiceImpl.java:953)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageInternal(DynamicThingsModelServiceImpl.java:504)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.processMessageTask(DynamicThingsModelServiceImpl.java:421)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:398)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 18 common frames omitted
08:38:35.595 [http-nio-5080-exec-47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 29395 millis. select device_id, device_name,product_id, serial_number,tenant_id, tenant_name, status, alarm_status, is_shadow,is_simulate, rssi ,location_way,things_model_value, active_time from iot_device
     
        where serial_number = ? ["Pressure001"]
08:39:23.317 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 96233 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"H_Pressure_1_Set"]
08:40:49.142 [http-nio-5080-exec-40] ERROR o.a.c.h.Http11NioProtocol - [log,175] - Failed to complete processing of a request
java.lang.OutOfMemoryError: Java heap space
08:41:55.772 [deviceUpMessageTask5] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:39:05.389 [http-nio-5080-exec-67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 74615 millis. select device_id, device_name,product_id, serial_number,tenant_id, tenant_name, status, alarm_status, is_shadow,is_simulate, rssi ,location_way,things_model_value, active_time from iot_device
     
        where serial_number = ? ["Pressure001"]
08:40:28.185 [http-nio-5080-exec-50] ERROR o.a.c.h.Http11NioProtocol - [log,175] - Failed to complete processing of a request
java.lang.OutOfMemoryError: Java heap space
08:40:07.045 [http-nio-5080-exec-82] ERROR o.a.c.h.Http11NioProtocol - [?,?] - Failed to complete processing of a request
java.lang.OutOfMemoryError: Java heap space
08:41:39.883 [deviceUpMessageTask14] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:42:39.276 [deviceUpMessageTask11] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:41:38.848 [deviceUpMessageTask12] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:42:40.897 [sip-1] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.OutOfMemoryError: Java heap space
08:42:40.897 [sip-24] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.OutOfMemoryError: Java heap space
08:40:54.925 [http-nio-5080-exec-7] ERROR o.a.c.h.Http11NioProtocol - [log,175] - Failed to complete processing of a request
java.lang.OutOfMemoryError: Java heap space
08:42:42.230 [MQTT Ping: server-*************] ERROR o.e.p.c.m.i.ClientState - [logToJsr47,210] - server-*************: Timed out as no activity, keepAlive=30,000,000,000 lastOutboundActivity=9,650,477,576,113,290 lastInboundActivity=9,650,478,632,696,194 time=9,650,511,316,926,780 lastPing=9,650,468,968,996,022
08:41:54.015 [deviceUpMessageTask1] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:42:42.232 [http-nio-5080-exec-50] ERROR o.a.t.u.n.NioEndpoint - [log,175] - Error running socket processor
java.lang.NullPointerException: null
	at org.apache.coyote.http11.Http11InputBuffer.recycle(Http11InputBuffer.java:268)
	at org.apache.coyote.http11.Http11Processor.recycle(Http11Processor.java:1430)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.release(AbstractProtocol.java:1094)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:1052)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
08:41:48.746 [dynamic-model-pool-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 126999 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"L_Pressure_1_Out"]
08:42:02.151 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 157010 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_Set"]
08:42:49.175 [sip-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 2398 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["44060400002000000001"]
08:42:52.723 [sip-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 6976 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["44060400002000000001"]
08:43:00.187 [deviceUpMessageTask8] ERROR o.s.t.i.TransactionInterceptor - [completeTransactionAfterThrowing,680] - Application exception overridden by rollback exception
java.lang.OutOfMemoryError: Java heap space
08:42:57.669 [sip-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1143 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["44060400002000000001"]
08:43:01.257 [sip-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 3588 millis. select d.device_id, d.device_name, d.product_id, p.product_name,p.device_type, d.tenant_id, d.tenant_name,
               d.serial_number, d.firmware_version, d.status,d.alarm_status, d.rssi,d.is_shadow,d.is_simulate ,d.location_way,d.things_model_value,
               d.network_address, d.network_ip, d.longitude, d.latitude, d.active_time, d.create_time, d.update_time,
               d.img_url,d.summary,d.remark from iot_device d
        left join iot_product p on p.product_id=d.product_id
        where d.serial_number = ? ["44060400002000000001"]
08:43:07.530 [deviceUpMessageTask8] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.BootstrapMethodError: call site initialization exception
	at java.lang.invoke.CallSite.makeSite(CallSite.java:341)
	at java.lang.invoke.MethodHandleNatives.linkCallSiteImpl(MethodHandleNatives.java:307)
	at java.lang.invoke.MethodHandleNatives.linkCallSite(MethodHandleNatives.java:297)
	at org.hibernate.engine.spi.ActionQueue.clear(ActionQueue.java:232)
	at org.hibernate.internal.SessionImpl.internalClear(SessionImpl.java:340)
	at org.hibernate.internal.SessionImpl.afterTransactionCompletion(SessionImpl.java:2399)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.afterTransactionCompletion(JdbcCoordinatorImpl.java:455)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.afterCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:203)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.access$400(JdbcResourceLocalTransactionCoordinatorImpl.java:40)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.rollback(JdbcResourceLocalTransactionCoordinatorImpl.java:305)
	at org.hibernate.engine.transaction.internal.TransactionImpl.rollback(TransactionImpl.java:142)
	at org.springframework.orm.jpa.JpaTransactionManager.doRollback(JpaTransactionManager.java:589)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processRollback(AbstractPlatformTransactionManager.java:835)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.rollback(AbstractPlatformTransactionManager.java:809)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.completeTransactionAfterThrowing(TransactionAspectSupport.java:672)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:392)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.OutOfMemoryError: Java heap space
08:43:07.532 [quartzScheduler_Worker-7] ERROR c.f.q.u.AbstractQuartzJob - [execute,49] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.fastbee.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:61)
	at com.fastbee.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.fastbee.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.fastbee.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:43)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: org.springframework.jdbc.UncategorizedSQLException: 
### Error updating database.  Cause: java.sql.SQLException: Error
### The error may exist in com/fastbee/iot/mapper/KeepPlanMapper.java (best guess)
### The error may involve com.fastbee.iot.mapper.KeepPlanMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE iot_keep_plan  SET name=?, village_ids=?,  content=?, times=?, gen_time_str=?, cycle=?, cycle_len=?,     status=?,  director_id=?,  organization_id=?, plan_type=?, is_del=?, is_gen_now=?, patrol_type=?, position_range=?, create_by=?, create_time=?, update_by=?, update_time=?  WHERE id=?
### Cause: java.sql.SQLException: Error
; uncategorized SQLException; SQL state [null]; error code [0]; Error; nested exception is java.sql.SQLException: Error
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy182.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy373.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.updateById(IService.java:239)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.saveOrUpdate(ServiceImpl.java:165)
	at com.fastbee.iot.service.impl.KeepPlanServiceImpl.generateWorkOrder(KeepPlanServiceImpl.java:234)
	at com.fastbee.iot.service.impl.KeepPlanServiceImpl$$FastClassBySpringCGLIB$$3498ce7.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.fastbee.iot.service.impl.KeepPlanServiceImpl$$EnhancerBySpringCGLIB$$1d5dd2c1.generateWorkOrder(<generated>)
	at com.fastbee.data.task.WorkOrderTask.generateWorkOrder(WorkOrderTask.java:52)
	... 10 common frames omitted
Caused by: java.sql.SQLException: Error
	at com.alibaba.druid.pool.DruidDataSource.handleConnectionException(DruidDataSource.java:1936)
	at com.alibaba.druid.pool.DruidPooledConnection.handleException(DruidPooledConnection.java:124)
	at com.alibaba.druid.pool.DruidPooledStatement.checkException(DruidPooledStatement.java:87)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:487)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.GeneratedMethodAccessor201.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy248.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.GeneratedMethodAccessor179.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy247.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor179.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy247.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at sun.reflect.GeneratedMethodAccessor221.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 26 common frames omitted
Caused by: java.lang.OutOfMemoryError: Java heap space
08:43:07.533 [deviceUpMessageTask20] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:43:07.533 [deviceUpMessageTask15] ERROR c.f.m.s.i.DataHandlerImpl - [reportData,100] - 接收属性数据，解析数据时异常 message=Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s),e={}
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceHashCommands.hGet(LettuceHashCommands.java:112)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.hGet(DefaultedRedisConnection.java:1145)
	at org.springframework.data.redis.core.DefaultHashOperations.lambda$get$0(DefaultHashOperations.java:53)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultHashOperations.get(DefaultHashOperations.java:53)
	at com.fastbee.common.core.redis.RedisCache.getCacheMapValue(RedisCache.java:222)
	at com.fastbee.iot.service.impl.ThingsModelServiceImpl.getSingleThingModels(ThingsModelServiceImpl.java:463)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.reportDeviceThingsModelValue(DeviceServiceImpl.java:305)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.reportDeviceThingsModelValue(<generated>)
	at com.fastbee.mqtt.service.impl.DataHandlerImpl.reportData(DataHandlerImpl.java:82)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.handlerReportMessage(DeviceReportMessageServiceImpl.java:369)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.processNoSub(DeviceReportMessageServiceImpl.java:256)
	at com.fastbee.mqtt.service.impl.DeviceReportMessageServiceImpl.parseReportMsg(DeviceReportMessageServiceImpl.java:117)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(DeviceReportMsgConsumer.java:28)
	at com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer$$FastClassBySpringCGLIB$$fe71c046.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 40 common frames omitted
08:43:07.532 [deviceUpMessageTask13] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:43:07.533 [deviceUpMessageTask17] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:43:07.533 [deviceUpMessageTask9] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.mq.redischannel.consumer.DeviceReportMsgConsumer.consume(com.fastbee.common.core.mq.DeviceReportBo)
java.lang.OutOfMemoryError: Java heap space
08:43:09.670 [http-nio-5080-exec-47] ERROR c.f.f.w.e.GlobalExceptionHandler - [handleException,80] - 请求地址'/iot/tool/mqtt/webhookv5',发生系统异常.
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.OutOfMemoryError: Java heap space
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1082)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.OutOfMemoryError: Java heap space
08:43:10.543 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1646 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"M_Pressure_1_HighLimit"]
08:43:10.541 [http-nio-5080-exec-67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1644 millis. select s.scene_id,s.chain_name
        from (select distinct scene_id from iot_scene_device where type = 2 and  (serial_number = ? OR product_id = ?)) d
        left join iot_scene s on s.scene_id=d.scene_id
        where s.`enable`= 1 ["Pressure001",141]
08:43:10.946 [quartzScheduler_Worker-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1275 millis. insert into sys_job_log(
 			 
 			 job_name, 
 			 job_group, 
 			 invoke_target, 
 			 job_message, 
 			 status, 
 			 exception_info, 
 			create_time
 		)values(
 			 
 			 ?, 
 			 ?, 
 			 ?, 
 			 ?, 
 			 ?, 
 			 ?, 
 			sysdate()
 		) ["巡检工单生成定时任务","DEFAULT","workOrderTask.generateWorkOrder()","巡检工单生成定时任务 总共耗时：453941毫秒","1","java.lang.reflect.InvocationTargetException\n\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Nati..."]
08:43:12.598 [deviceUpMessageTask2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1180 millis. select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               temp_slave_id,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               reverse_formula,
               reg_addr,
               is_monitor,
               del_flag,
               bit_option,
               value_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order,
               quantity,
               code,
               parse_type,
               plc_ids
        from iot_things_model
     
         WHERE  product_id = ?
            
            
                and identifier = ? 
        limit 1 [146,"O_WaterBox_1_Level"]
08:43:14.704 [http-nio-5080-exec-91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1071 millis. update iot_device
         SET status = ?,
            alarm_status = ?,
            
            
            
            
            active_time = ? 
        where serial_number = ? or gw_dev_code = ? [4,0,"2025-07-16 08:00:00","Pressure001","Pressure001"]
08:43:16.837 [http-nio-5080-exec-91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1057 millis. insert into iot_event_log
         ( identity,
            
            log_type,
            log_value,
            device_id,
            device_name,
            serial_number,
            is_monitor,
            mode,
            user_id,
            user_name,
            
            
            
            create_time,
            remark ) 
         values ( ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            ?,
            ? ) ["offline",6,"0",62245,"4G压力传感器厂家测试","Pressure001",0,3,1,"admin","2025-07-31 08:43:15","设备离线"]
08:43:18.601 [sip-23] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
org.springframework.jdbc.UncategorizedSQLException: 
### Error querying database.  Cause: java.sql.SQLException: Error
### The error may exist in URL [jar:file:/iscsi-data/openredhead/projects/gmeg/jar/gmeg-server.jar!/BOOT-INF/lib/sip-server-3.8.5.jar!/mapper/SipDeviceMapper.xml]
### The error may involve com.fastbee.sip.mapper.SipDeviceMapper.selectSipDeviceBySipId-Inline
### The error occurred while setting parameters
### SQL: select device_id,                product_id,                product_name,                device_sip_id,                device_name,                manufacturer,                model,                firmware,                transport,                streamMode,                online,                registerTime,                lastConnectTime,                active_time,                ip,                port,                hostAddress,                del_flag,                create_by,                create_time,                update_by,                update_time,                remark         from sip_device               where device_sip_id = ?
### Cause: java.sql.SQLException: Error
; uncategorized SQLException; SQL state [null]; error code [0]; Error; nested exception is java.sql.SQLException: Error
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy182.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy400.selectSipDeviceBySipId(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor746.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy401.selectSipDeviceBySipId(Unknown Source)
	at com.fastbee.sip.service.impl.SipDeviceServiceImpl.selectSipDeviceBySipId(SipDeviceServiceImpl.java:71)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:52)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.GeneratedMethodAccessor789.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: Error
	at com.alibaba.druid.pool.DruidDataSource.handleConnectionException(DruidDataSource.java:1936)
	at com.alibaba.druid.pool.DruidPooledConnection.handleException(DruidPooledConnection.java:124)
	at com.alibaba.druid.pool.DruidPooledStatement.checkException(DruidPooledStatement.java:87)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:487)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor175.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy248.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy247.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy247.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.GeneratedMethodAccessor180.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 30 common frames omitted
Caused by: java.lang.OutOfMemoryError: Java heap space
08:43:19.313 [http-nio-5080-exec-33] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/141/Pressure001/status/post,message=等待来自服务器的响应时超时
08:43:19.670 [http-nio-5080-exec-91] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/141/Pressure001/status/post,message=客户机未连接
08:43:19.670 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1068 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"5525","BIGUIGUANGCHANG","H_Pump_1_Freq",0,2,"2025-07-31 08:43:12","1号泵频率"]
08:43:21.113 [http-nio-5080-exec-33] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/141/Pressure001/function/get,message=客户机未连接
08:43:21.792 [http-nio-5080-exec-91] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/141/Pressure001/function/get,message=客户机未连接
08:43:21.792 [deviceUpMessageTask19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,518] - slow sql 1062 millis. insert into iot_device_log
         ( log_type,
            log_value,
            
            
            serial_number,
            identity,
            
            is_monitor,
            mode,
            create_time,
            
            
            
            
            
            model_name ) 
         values ( ?,
            ?,
            
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            
            
            
            
            
            ? ) [1,"5532","BIGUIGUANGCHANG","H_Pump_2_Freq",0,2,"2025-07-31 08:43:14","2号泵频率"]
08:43:23.862 [http-nio-5080-exec-33] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/141/Pressure001/function/get,message=客户机未连接
08:43:24.976 [deviceUpMessageTask19] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/146/BIGUIGUANGCHANG/ws/service,message=客户机未连接
08:43:24.236 [http-nio-5080-exec-91] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/141/Pressure001/function/get,message=客户机未连接
08:43:26.119 [http-nio-5080-exec-45] ERROR c.f.d.c.ToolController - [webHookProcessv5,418] - 发生错误: 
### Error querying database.  Cause: java.sql.SQLException: Java heap space
### The error may exist in URL [jar:file:/iscsi-data/openredhead/projects/gmeg/jar/gmeg-server.jar!/BOOT-INF/lib/fastbee-iot-service-3.8.5.jar!/mapper/iot/DeviceMapper.xml]
### The error may involve com.fastbee.iot.mapper.DeviceMapper.selectShortDeviceBySerialNumber-Inline
### The error occurred while setting parameters
### SQL: select device_id, device_name,product_id, serial_number,tenant_id, tenant_name, status, alarm_status, is_shadow,is_simulate, rssi ,location_way,things_model_value, active_time from iot_device               where serial_number = ?
### Cause: java.sql.SQLException: Java heap space
; uncategorized SQLException; SQL state [HY001]; error code [0]; Java heap space; nested exception is java.sql.SQLException: Java heap space
org.springframework.jdbc.UncategorizedSQLException: 
### Error querying database.  Cause: java.sql.SQLException: Java heap space
### The error may exist in URL [jar:file:/iscsi-data/openredhead/projects/gmeg/jar/gmeg-server.jar!/BOOT-INF/lib/fastbee-iot-service-3.8.5.jar!/mapper/iot/DeviceMapper.xml]
### The error may involve com.fastbee.iot.mapper.DeviceMapper.selectShortDeviceBySerialNumber-Inline
### The error occurred while setting parameters
### SQL: select device_id, device_name,product_id, serial_number,tenant_id, tenant_name, status, alarm_status, is_shadow,is_simulate, rssi ,location_way,things_model_value, active_time from iot_device               where serial_number = ?
### Cause: java.sql.SQLException: Java heap space
; uncategorized SQLException; SQL state [HY001]; error code [0]; Java heap space; nested exception is java.sql.SQLException: Java heap space
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy182.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy287.selectShortDeviceBySerialNumber(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor407.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy288.selectShortDeviceBySerialNumber(Unknown Source)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.selectShortDeviceBySerialNumber(DeviceServiceImpl.java:252)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$cebb6ca9.selectShortDeviceBySerialNumber(<generated>)
	at com.fastbee.data.controller.ToolController.webHookProcessv5(ToolController.java:360)
	at com.fastbee.data.controller.ToolController$$FastClassBySpringCGLIB$$63407c17.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.data.controller.ToolController$$EnhancerBySpringCGLIB$$ad00a7c9.webHookProcessv5(<generated>)
	at sun.reflect.GeneratedMethodAccessor242.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: Java heap space
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:129)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor175.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy248.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy247.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy247.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.GeneratedMethodAccessor180.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 142 common frames omitted
Caused by: java.lang.OutOfMemoryError: Java heap space
08:43:27.131 [http-nio-5080-exec-45] ERROR c.f.d.c.ToolController - [webHookProcessv5,422] - 错误位置: org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible:92
08:43:28.266 [deviceUpMessageTask2] ERROR c.f.m.PubMqttClient - [publish,231] - =>发布主题时发生错误 topic=/146/BIGUIGUANGCHANG/ws/service,message=客户机未连接
09:27:04.753 [deviceOtherMsgTask1] ERROR c.f.m.r.l.DeviceOtherListen - [listen,31] - =>emq数据转发异常
09:27:04.755 [messageConsumeTaskFetch1] ERROR c.f.m.r.l.DevicePropFetchListen - [listen,33] - =>设备属性获取异常
java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2048)
	at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
	at com.fastbee.mq.redischannel.queue.DevicePropFetchQueue.take(DevicePropFetchQueue.java:24)
	at com.fastbee.mq.redischannel.listen.DevicePropFetchListen.listen(DevicePropFetchListen.java:29)
	at com.fastbee.mq.redischannel.listen.DevicePropFetchListen$$FastClassBySpringCGLIB$$4e693952.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:04.756 [messageConsumeTaskPub2] ERROR c.f.m.r.l.DeviceReportListen - [listen,36] - =>设备上报数据监听异常
java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2048)
	at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
	at com.fastbee.mq.redischannel.queue.DeviceReportQueue.take(DeviceReportQueue.java:23)
	at com.fastbee.mq.redischannel.listen.DeviceReportListen.listen(DeviceReportListen.java:32)
	at com.fastbee.mq.redischannel.listen.DeviceReportListen$$FastClassBySpringCGLIB$$75c9cd07.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:04.756 [messageConsumeTask2] ERROR c.f.m.r.l.UpgradeListen - [listen,34] - ->OTA消息监听异常
java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2048)
	at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
	at com.fastbee.mq.redischannel.queue.OtaUpgradeQueue.take(OtaUpgradeQueue.java:23)
	at com.fastbee.mq.redischannel.listen.UpgradeListen.listen(UpgradeListen.java:30)
	at com.fastbee.mq.redischannel.listen.UpgradeListen$$FastClassBySpringCGLIB$$da7b29cd.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:04.756 [messageConsumeTaskPub1] ERROR c.f.m.r.l.DeviceReplyListen - [listen,35] - =>设备回调消息监听异常
java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2048)
	at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
	at com.fastbee.mq.redischannel.queue.DeviceReplyQueue.take(DeviceReplyQueue.java:23)
	at com.fastbee.mq.redischannel.listen.DeviceReplyListen.listen(DeviceReplyListen.java:31)
	at com.fastbee.mq.redischannel.listen.DeviceReplyListen$$FastClassBySpringCGLIB$$7ff132e5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:04.756 [messageConsumeTask3] ERROR c.f.m.r.l.FunctionInvokeListen - [listen,31] - =>下发服务消费异常
java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2048)
	at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
	at com.fastbee.mq.redischannel.queue.FunctionInvokeQueue.take(FunctionInvokeQueue.java:23)
	at com.fastbee.mq.redischannel.listen.FunctionInvokeListen.listen(FunctionInvokeListen.java:28)
	at com.fastbee.mq.redischannel.listen.FunctionInvokeListen$$FastClassBySpringCGLIB$$cf27564d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:04.756 [messageConsumeTask1] ERROR c.f.m.r.l.DeviceStatusListen - [listen,31] - 设备状态监听错误
java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2048)
	at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
	at com.fastbee.mq.redischannel.queue.DeviceStatusQueue.take(DeviceStatusQueue.java:23)
	at com.fastbee.mq.redischannel.listen.DeviceStatusListen.listen(DeviceStatusListen.java:27)
	at com.fastbee.mq.redischannel.listen.DeviceStatusListen$$FastClassBySpringCGLIB$$cea7c145.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-2] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-13] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-24] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-17] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-23] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-8] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.789 [dynamic-model-pool-5] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.790 [dynamic-model-pool-18] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.790 [dynamic-model-pool-22] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-12] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-10] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-3] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-16] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-21] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-20] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-4] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.790 [dynamic-model-pool-9] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.790 [dynamic-model-pool-1] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.790 [dynamic-model-pool-14] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.786 [dynamic-model-pool-7] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:34.789 [dynamic-model-pool-19] ERROR c.f.i.s.i.DynamicThingsModelServiceImpl - [lambda$startMessageProcessing$0,405] - 消息处理线程被中断
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at com.fastbee.iot.service.impl.DynamicThingsModelServiceImpl.lambda$startMessageProcessing$0(DynamicThingsModelServiceImpl.java:401)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:27:49.825 [main] ERROR c.f.s.u.ZlmApiUtils - [getMediaServerConfig,96] - 获取流媒体服务配置失败！
09:28:11.922 [http-nio-5080-exec-2] ERROR c.f.c.u.h.HttpUtils - [sendGet,97] - 调用HttpUtils.sendGet IOException, url=https://api.map.baidu.com/geocoder,param=address=广州市&output=json
javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1573)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1390)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1295)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:417)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:558)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:201)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:167)
	at com.fastbee.common.utils.http.HttpUtils.sendGet(HttpUtils.java:78)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.setLatitudeAndLongitude(DeviceServiceImpl.java:1436)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.setLocation(DeviceServiceImpl.java:1422)
	at com.fastbee.iot.service.impl.DeviceServiceImpl.updateDeviceStatusAndLocation(DeviceServiceImpl.java:1082)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$FastClassBySpringCGLIB$$68fb21df.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.iot.service.impl.DeviceServiceImpl$$EnhancerBySpringCGLIB$$472f40d3.updateDeviceStatusAndLocation(<generated>)
	at com.fastbee.data.controller.ToolController.webHookProcessv5(ToolController.java:390)
	at com.fastbee.data.controller.ToolController$$FastClassBySpringCGLIB$$63407c17.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.data.controller.ToolController$$EnhancerBySpringCGLIB$$de0e3a76.webHookProcessv5(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:480)
	at sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:469)
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:159)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1382)
	... 142 common frames omitted
09:28:12.254 [http-nio-5080-exec-2] ERROR c.f.d.c.ToolController - [webHookProcessv5,418] - 发生错误: null
java.lang.NullPointerException: null
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.mqtt.service.impl.MqttMessagePublishImpl.publishStatus(MqttMessagePublishImpl.java:377)
	at com.fastbee.data.controller.ToolController.webHookProcessv5(ToolController.java:392)
	at com.fastbee.data.controller.ToolController$$FastClassBySpringCGLIB$$63407c17.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.data.controller.ToolController$$EnhancerBySpringCGLIB$$de0e3a76.webHookProcessv5(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
09:28:12.255 [http-nio-5080-exec-2] ERROR c.f.d.c.ToolController - [webHookProcessv5,422] - 错误位置: com.fastbee.mqttclient.PubMqttClient.publish:225
09:28:12.988 [http-nio-5080-exec-6] ERROR c.f.d.c.ToolController - [webHookProcessv5,418] - 发生错误: null
java.lang.NullPointerException: null
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.mqtt.service.impl.MqttMessagePublishImpl.publishStatus(MqttMessagePublishImpl.java:377)
	at com.fastbee.data.controller.ToolController.webHookProcessv5(ToolController.java:392)
	at com.fastbee.data.controller.ToolController$$FastClassBySpringCGLIB$$63407c17.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.fastbee.data.controller.ToolController$$EnhancerBySpringCGLIB$$de0e3a76.webHookProcessv5(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fastbee.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.fastbee.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
09:28:12.989 [http-nio-5080-exec-6] ERROR c.f.d.c.ToolController - [webHookProcessv5,422] - 错误位置: com.fastbee.mqttclient.PubMqttClient.publish:225
09:28:37.659 [sip-4] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
09:29:07.607 [sip-5] ERROR o.s.a.i.SimpleAsyncUncaughtExceptionHandler - [handleUncaughtException,39] - Unexpected exception occurred invoking async method: public void com.fastbee.sip.server.impl.GBListenerImpl.processRequest(javax.sip.RequestEvent)
java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366)
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348)
	at com.fastbee.mqttclient.PubMqttClient.publish(PubMqttClient.java:225)
	at com.fastbee.sip.service.impl.VideoMqttService.publishInfo(VideoMqttService.java:71)
	at com.fastbee.sip.handler.req.message.notify.cmdType.KeepaliveHandler.handlerCmdType(KeepaliveHandler.java:63)
	at com.fastbee.sip.handler.req.message.MessageHandlerAbstract.handlerCmdType(MessageHandlerAbstract.java:24)
	at com.fastbee.sip.handler.req.message.MessageRequestProcessor.processMsg(MessageRequestProcessor.java:77)
	at com.fastbee.sip.server.impl.GBListenerImpl.processRequest(GBListenerImpl.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)